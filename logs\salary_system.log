2025-09-01 13:22:36.959 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-09-01 13:22:36.959 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-09-01 13:22:36.960 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-09-01 13:22:36.961 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-09-01 13:22:36.962 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-09-01 13:22:36.962 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-09-01 13:22:40.149 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-09-01 13:22:40.150 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-09-01 13:22:40.151 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-09-01 13:22:40.151 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-09-01 13:22:40.152 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-09-01 13:22:40.152 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-09-01 13:22:40.153 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-01 13:22:40.156 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-01 13:22:40.158 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-01 13:22:40.159 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-01 13:22:40.166 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-09-01 13:22:40.179 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-09-01 13:22:40.179 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-09-01 13:22:40.180 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-01 13:22:40.188 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-09-01 13:22:40.189 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-09-01 13:22:40.192 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-09-01 13:22:40.193 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-09-01 13:22:40.194 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-01 13:22:40.195 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-09-01 13:22:40.196 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-09-01 13:22:40.197 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-09-01 13:22:40.204 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-09-01 13:22:40.205 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11899 | 🔧 [P2-3] 错误恢复策略注册完成
2025-09-01 13:22:40.206 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-01 13:22:40.207 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11754 | 🔧 [P2-3] 错误处理机制设置完成
2025-09-01 13:22:40.208 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11792 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-09-01 13:22:40.421 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-09-01 13:22:40.421 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-09-01 13:22:40.424 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-01 13:22:40.427 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-09-01 13:22:40.428 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-09-01 13:22:40.429 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-01 13:22:40.430 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-01 13:22:40.432 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-09-01 13:22:40.439 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-09-01 13:22:40.441 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-09-01 13:22:40.442 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-09-01 13:22:40.443 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-09-01 13:22:40.444 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-01 13:22:40.445 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-01 13:22:40.447 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-01 13:22:40.452 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-01 13:22:40.453 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-09-01 13:22:40.454 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 29.1ms
2025-09-01 13:22:40.481 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-09-01 13:22:40.482 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-09-01 13:22:40.485 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-09-01 13:22:40.487 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-09-01 13:22:40.487 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-09-01 13:22:40.488 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-09-01 13:22:40.489 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-09-01 13:22:40.490 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-09-01 13:22:40.491 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-09-01 13:22:40.491 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-09-01 13:22:40.495 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-09-01 13:22:40.515 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-09-01 13:22:40.777 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-09-01 13:22:40.779 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-09-01 13:22:40.782 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-09-01 13:22:40.783 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-09-01 13:22:40.783 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-09-01 13:22:40.784 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-09-01 13:22:40.788 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-09-01 13:22:40.788 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-09-01 13:22:40.789 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-09-01 13:22:40.803 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-01 13:22:40.803 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-01 13:22:40.804 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-01 13:22:40.809 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-09-01 13:22:40.811 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-01 13:22:40.814 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-01 13:22:40.828 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-01 13:22:40.829 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-09-01 13:22:40.839 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-09-01 13:22:40.843 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-09-01 13:22:40.844 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-01 13:22:40.854 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表']
2025-09-01 13:22:40.855 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 7个展开项
2025-09-01 13:22:40.856 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > A岗职工', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > 退休人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表']
2025-09-01 13:22:40.857 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-09-01 13:22:40.864 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-09-01 13:22:40.865 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-01 13:22:40.867 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-01 13:22:40.874 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-01 13:22:40.877 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-09-01 13:22:40.882 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-09-01 13:22:40.885 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-01 13:22:40.919 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > 全部在职人员工资表', '异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年 > 12月']
2025-09-01 13:22:40.922 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > 全部在职人员工资表', '异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年 > 12月']
2025-09-01 13:22:40.923 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-09-01 13:22:40.924 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-09-01 13:22:40.928 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-01 13:22:40.931 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-01 13:22:40.932 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-09-01 13:22:40.933 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-09-01 13:22:40.934 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-09-01 13:22:41.218 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-09-01 13:22:41.220 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-09-01 13:22:41.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-09-01 13:22:41.229 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-09-01 13:22:41.266 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-09-01 13:22:41.266 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-09-01 13:22:41.271 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-09-01 13:22:41.273 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-09-01 13:22:41.273 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-09-01 13:22:41.274 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-01 13:22:41.275 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-01 13:22:41.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-09-01 13:22:41.286 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-09-01 13:22:41.287 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-09-01 13:22:41.288 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-09-01 13:22:41.289 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-09-01 13:22:41.290 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-09-01 13:22:41.291 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-09-01 13:22:41.296 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-09-01 13:22:41.297 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-09-01 13:22:41.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-09-01 13:22:41.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-09-01 13:22:41.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-09-01 13:22:41.301 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-01 13:22:41.302 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-01 13:22:41.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-01 13:22:41.311 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-09-01 13:22:41.312 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-09-01 13:22:41.312 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-09-01 13:22:41.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-01 13:22:41.322 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-09-01 13:22:41.324 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-09-01 13:22:41.325 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-01 13:22:41.342 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-01 13:22:41.342 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-01 13:22:41.343 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-01 13:22:41.345 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-01 13:22:41.379 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-09-01 13:22:41.381 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-09-01 13:22:41.406 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 77.3ms
2025-09-01 13:22:41.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-01 13:22:41.410 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-01 13:22:41.414 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-09-01 13:22:41.415 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-01 13:22:41.416 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-01 13:22:41.429 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-09-01 13:22:41.447 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-09-01 13:22:41.448 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-09-01 13:22:41.496 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-09-01 13:22:41.549 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-09-01 13:22:41.550 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-09-01 13:22:41.554 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-09-01 13:22:41.555 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-09-01 13:22:41.556 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-09-01 13:22:41.557 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-09-01 13:22:41.561 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-09-01 13:22:41.566 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-09-01 13:22:41.567 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6931 | 🔧 [P1-2修复] 发现 7 个表的配置
2025-09-01 13:22:41.568 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-09-01 13:22:41.570 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-09-01 13:22:41.570 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_全部在职人员工资表, 30个字段
2025-09-01 13:22:41.571 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_退休人员工资表, 30个字段
2025-09-01 13:22:41.572 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_A岗职工, 30个字段
2025-09-01 13:22:41.573 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_离休人员工资表, 30个字段
2025-09-01 13:22:41.580 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: salary_data_2025_05, 30个字段
2025-09-01 13:22:41.580 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6941 | ✅ [P1-2修复] 已加载字段映射信息，共7个表的映射
2025-09-01 13:22:41.588 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-01 13:22:41.598 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-01 13:22:41.599 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-01 13:22:41.600 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-01 13:22:41.601 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-01 13:22:41.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-09-01 13:22:41.603 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-01 13:22:41.603 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-01 13:22:41.604 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-01 13:22:41.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-01 13:22:41.606 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 5.6ms
2025-09-01 13:22:41.607 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-01 13:22:41.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-01 13:22:41.615 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-09-01 13:22:41.616 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-01 13:22:41.617 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-01 13:22:41.618 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-09-01 13:22:41.619 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8633 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-09-01 13:22:41.622 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-01 13:22:41.629 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-01 13:22:41.631 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-01 13:22:41.632 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-01 13:22:41.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-01 13:22:41.633 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-01 13:22:41.634 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-01 13:22:41.635 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-01 13:22:41.636 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-01 13:22:41.638 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 6.2ms
2025-09-01 13:22:41.644 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-01 13:22:41.645 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-01 13:22:41.648 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-09-01 13:22:41.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-01 13:22:41.649 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-01 13:22:41.676 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8651 | 已显示标准空表格，表头数量: 22
2025-09-01 13:22:41.688 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-09-01 13:22:41.699 | INFO     | src.gui.style_manager:update_responsive_style:227 | 响应式样式更新: medium -> small
2025-09-01 13:22:41.700 | INFO     | src.gui.style_manager:update_responsive_style:238 | 响应式样式更新成功: small
2025-09-01 13:22:41.701 | INFO     | src.gui.style_manager:update_responsive_style:227 | 响应式样式更新: small -> medium
2025-09-01 13:22:41.701 | INFO     | src.gui.style_manager:update_responsive_style:238 | 响应式样式更新成功: medium
2025-09-01 13:22:41.863 | INFO     | __main__:main:514 | 应用程序启动成功
2025-09-01 13:22:41.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-01 13:22:41.870 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-09-01 13:22:41.872 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-01 13:22:41.872 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-01 13:22:41.933 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-09-01 13:22:41.935 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-09-01 13:22:42.025 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-01 13:22:42.029 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-01 13:22:42.029 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-09-01 13:22:42.445 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9535 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-09-01 13:22:42.445 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9445 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-09-01 13:22:42.449 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9459 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-09-01 13:22:42.449 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9993 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-09-01 13:22:42.466 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9465 | 🔧 [P0-1] 智能显示亮度修复完成
2025-09-01 13:22:43.031 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-09-01 13:22:43.031 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-01 13:22:43.038 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-01 13:22:43.038 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-09-01 13:22:44.040 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-01 13:22:44.042 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-01 13:22:44.043 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-09-01 13:22:44.045 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-09-01 13:22:56.348 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-09-01 13:22:56.349 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8456 | 检测到当前在工资表TAB，生成工资表默认路径
2025-09-01 13:22:56.352 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 09月 > 全部在职人员。打开导入对话框。
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 初始化统一数据导入窗口
2025-09-01 13:22:56.437 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-01 13:22:56.440 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-01 13:22:56.441 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-01 13:22:56.442 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-01 13:22:56.443 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-09-01 13:22:56.452 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-09-01 13:22:56.454 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-09-01 13:22:56.455 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-01 13:22:56.461 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-09-01 13:22:56.462 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-09-01 13:22:56.469 | INFO     | src.core.config_cache_manager:__init__:67 | 配置缓存管理器初始化完成: cache\config_cache, 最大条目数: 100
2025-09-01 13:22:56.469 | INFO     | src.modules.data_import.change_data_config_manager:__init__:69 | 配置缓存已启用
2025-09-01 13:22:56.470 | INFO     | src.modules.data_import.change_data_config_manager:__init__:75 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-09-01 13:22:56.472 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-09-01 13:22:56 - src.gui.core.smart_mapping_engine - INFO - 成功加载历史配置: 3 个配置
2025-09-01 13:22:56 - src.gui.core.smart_mapping_engine - INFO - 智能映射引擎初始化完成
2025-09-01 13:22:56 - src.gui.core.template_manager - INFO - 模板管理器初始化完成: 内置模板 3 个, 用户模板 0 个
2025-09-01 13:22:56 - src.gui.core.validation_engine - INFO - 验证引擎初始化完成
2025-09-01 13:22:56.544 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 统一导入管理器初始化完成（包含第二阶段功能）
2025-09-01 13:22:56.549 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 核心组件初始化成功
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 接收到高级配置变化: {'file_import': {'default_import_path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'supported_formats': ['xlsx', 'xls', 'csv'], 'max_file_size_mb': 100, 'auto_detect_encoding': True, 'sheet_selection_strategy': 'all', 'data_start_row': 2, 'header_row': 1, 'skip_rows': 0, 'skip_footer_rows': 0, 'auto_detect_header': True, 'ignore_empty_rows': True}, 'field_mapping': {'mapping_algorithm': 'fuzzy_match', 'similarity_threshold': 80, 'auto_mapping_enabled': True, 'required_field_check': True, 'field_type_validation': True, 'save_mapping_history': True}, 'smart_recommendations': {'confidence_threshold': 70, 'enable_history_learning': True, 'enable_semantic_analysis': True, 'auto_apply_high_confidence': False, 'template_priority': 0, 'max_saved_templates': 50}, 'data_processing': {'strict_validation': False, 'null_value_strategy': 0, 'auto_type_conversion': True, 'duplicate_strategy': 0, 'batch_size': 1000, 'error_tolerance': 10, 'remove_duplicates': False, 'handle_missing_values': 'keep', 'format_numbers': True, 'format_dates': True, 'trim_whitespace': True, 'convert_data_types': True, 'data_validation': True, 'custom_rules': [], 'saved_templates': []}, 'ui_customization': {'table_row_limit': 200, 'show_detailed_logs': False, 'show_confidence_indicators': True, 'auto_save_interval': 5, 'show_confirmation_dialogs': True, 'show_shortcuts': True}, 'performance': {'max_memory_usage': 2048, 'enable_caching': True, 'preload_data': False, 'thread_count': 4, 'enable_async_processing': True, 'progress_update_frequency': 100}}
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 应用默认导入文件: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - UI未初始化，保存默认路径供后续应用: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 文件导入配置已应用，包括Excel表结构设置: {'data_start_row': 2, 'header_row': 1, 'skip_rows': 0, 'skip_footer_rows': 0, 'auto_detect_header': True, 'ignore_empty_rows': True}
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 字段映射配置已应用
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 智能推荐配置已应用
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 数据处理配置已应用
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 界面个性化配置已应用
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 性能优化配置已应用
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 所有高级配置域已成功应用: ['file_import', 'field_mapping', 'smart_recommendations', 'data_processing', 'ui_customization', 'performance']
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 🔧 [P0修复] 已加载保存的高级配置
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 工作线程初始化完成
2025-09-01 13:22:56.573 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-09-01 13:22:56 - src.gui.core.smart_mapping_engine - INFO - 成功加载历史配置: 3 个配置
2025-09-01 13:22:56 - src.gui.core.smart_mapping_engine - INFO - 智能映射引擎初始化完成
2025-09-01 13:22:56 - src.gui.core.template_manager - INFO - 模板管理器初始化完成: 内置模板 3 个, 用户模板 0 个
2025-09-01 13:22:56 - src.gui.core.validation_engine - INFO - 验证引擎初始化完成
2025-09-01 13:22:56 - src.gui.performance_optimizer - INFO - 性能优化器初始化完成
2025-09-01 13:22:56.614 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-01 13:22:56.696 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-01 13:22:56.697 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: number
2025-09-01 13:22:56.700 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: string
2025-09-01 13:22:56.701 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: date
2025-09-01 13:22:56.701 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: code
2025-09-01 13:22:56.702 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: custom
2025-09-01 13:22:56.703 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: salary_float - 工资金额
2025-09-01 13:22:56.704 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: employee_id_string - 工号
2025-09-01 13:22:56.704 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: name_string - 姓名
2025-09-01 13:22:56.705 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: date_string - 日期
2025-09-01 13:22:56.706 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: id_number_string - 身份证号
2025-09-01 13:22:56.707 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: code_string - 代码
2025-09-01 13:22:56.715 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: float - 浮点数
2025-09-01 13:22:56.716 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: integer - 整数
2025-09-01 13:22:56.717 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: text_string - 文本字符串
2025-09-01 13:22:56.717 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: personnel_category_code - 人员类别代码
2025-09-01 13:22:56 - src.gui.widgets.field_type_config_widget - INFO - 已加载 10 个内置类型和 0 个自定义类型
2025-09-01 13:22:56 - src.gui.widgets.field_type_config_widget - INFO - 字段类型配置组件初始化完成
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - UI界面创建完成
2025-09-01 13:22:56 - src.gui.unified_data_import_window - INFO - 应用待处理的默认文件: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-09-01 13:22:57.041 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-09-01 13:22:57.042 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-01 13:22:57.045 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-01 13:22:57.046 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-01 13:22:57.160 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-01 13:22:57.164 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-01 13:22:57.166 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-01 13:22:57.166 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-01 13:22:57.170 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-01 13:22:57.174 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-01 13:22:57.177 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-01 13:22:57.178 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-01 13:22:57.291 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-01 13:22:57.292 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-09-01 13:22:57.295 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-01 13:22:57.402 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-01 13:22:57.404 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-09-01 13:22:57.408 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-01 13:22:57.509 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-01 13:22:57.511 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-01 13:22:57 - src.gui.unified_data_import_window - INFO - 已加载 4 个Sheet
2025-09-01 13:22:57 - src.gui.unified_data_import_window - INFO - 成功加载 4 个工作表
2025-09-01 13:22:57 - src.gui.unified_data_import_window - INFO - 待处理配置应用完成: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-09-01 13:22:57 - src.gui.unified_data_import_window - INFO - 信号连接完成
2025-09-01 13:22:57 - src.gui.unified_data_import_window - INFO - 统一数据导入窗口初始化完成
2025-09-01 13:22:57 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=307px, 右侧=1090px
2025-09-01 13:22:57 - src.gui.unified_data_import_window - INFO - 响应式布局初始化完成
2025-09-01 13:22:57 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=307px, 右侧=1090px
2025-09-01 13:23:15 - src.gui.unified_data_import_window - INFO - 选中Sheet: A岗职工
2025-09-01 13:23:15.132 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet 'A岗职工' 创建默认配置
2025-09-01 13:23:15 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: A岗职工
2025-09-01 13:23:15.137 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet 'A岗职工' 创建默认配置
2025-09-01 13:23:15.147 | INFO     | src.modules.data_import.config_template_manager:_load_templates:579 | 加载了 10 个模板
2025-09-01 13:23:15.151 | INFO     | src.modules.data_import.config_template_manager:_init_builtin_templates:184 | 初始化了 4 个内置模板
2025-09-01 13:23:15.151 | INFO     | src.modules.data_import.config_template_manager:__init__:100 | 配置模板管理器初始化完成，模板目录: state\config_templates
2025-09-01 13:23:15.152 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-01 13:23:15.153 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: A岗职工
2025-09-01 13:23:15 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: A岗职工
2025-09-01 13:23:15.155 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-01 13:23:15.156 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-01 13:23:15.157 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-01 13:23:15.271 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-09-01 13:23:15.275 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-01 13:23:15.277 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-01 13:23:15.277 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 21列
2025-09-01 13:23:15.281 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 21列
2025-09-01 13:23:15 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 21 个
2025-09-01 13:23:15 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 21 个字段, 表类型: 
2025-09-01 13:23:15 - src.gui.unified_data_import_window - ERROR - 更新Sheet字段映射失败: 'QWidget' object has no attribute 'currentTextChanged'
2025-09-01 13:23:15 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: A岗职工
2025-09-01 13:23:15.477 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-01 13:23:15.585 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-01 13:23:15.587 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-01 13:23:15.590 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-01 13:23:15.593 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-09-01 13:23:15 - src.gui.unified_data_import_window - INFO - 预览数据加载完成: A岗职工 - 62 行
2025-09-01 13:23:15 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 62 行, 21 列
2025-09-01 13:23:15 - src.gui.unified_data_import_window - INFO - 已加载Sheet 'A岗职工' 的预览数据: 62 行
2025-09-01 13:24:03 - src.gui.unified_data_import_window - INFO - 选中Sheet: 退休人员工资表
2025-09-01 13:24:03.292 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '退休人员工资表' 创建默认配置
2025-09-01 13:24:03 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: 退休人员工资表
2025-09-01 13:24:03.297 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '退休人员工资表' 创建默认配置
2025-09-01 13:24:03.299 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-01 13:24:03.299 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 退休人员工资表
2025-09-01 13:24:03 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: 退休人员工资表
2025-09-01 13:24:03.301 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-01 13:24:03.301 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-01 13:24:03.302 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-01 13:24:03.407 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-09-01 13:24:03.411 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-01 13:24:03.413 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-09-01 13:24:03.413 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 27列
2025-09-01 13:24:03.416 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 27列
2025-09-01 13:24:03 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 27 个
2025-09-01 13:24:03 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 27 个字段, 表类型: 
2025-09-01 13:24:03 - src.gui.unified_data_import_window - ERROR - 更新Sheet字段映射失败: 'QWidget' object has no attribute 'currentTextChanged'
2025-09-01 13:24:03 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 退休人员工资表
2025-09-01 13:24:03.658 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-01 13:24:03.762 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-01 13:24:03.764 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-09-01 13:24:03.766 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-01 13:24:03.767 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-09-01 13:24:03 - src.gui.unified_data_import_window - INFO - 预览数据加载完成: 退休人员工资表 - 13 行
2025-09-01 13:24:03 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 13 行, 27 列
2025-09-01 13:24:03 - src.gui.unified_data_import_window - INFO - 已加载Sheet '退休人员工资表' 的预览数据: 13 行
2025-09-01 13:24:05 - src.gui.unified_data_import_window - INFO - 选中Sheet: 全部在职人员工资表
2025-09-01 13:24:05.757 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '全部在职人员工资表' 创建默认配置
2025-09-01 13:24:05 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: 全部在职人员工资表
2025-09-01 13:24:05.761 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '全部在职人员工资表' 创建默认配置
2025-09-01 13:24:05.763 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-01 13:24:05.763 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 全部在职人员工资表
2025-09-01 13:24:05 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: 全部在职人员工资表
2025-09-01 13:24:05.765 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-01 13:24:05.766 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-01 13:24:05.766 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-01 13:24:05.866 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-09-01 13:24:05.870 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-01 13:24:05.872 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-09-01 13:24:05.872 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 23列
2025-09-01 13:24:05.876 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 23列
2025-09-01 13:24:05 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 23 个
2025-09-01 13:24:05 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 23 个字段, 表类型: 
2025-09-01 13:24:06 - src.gui.unified_data_import_window - ERROR - 更新Sheet字段映射失败: 'QWidget' object has no attribute 'currentTextChanged'
2025-09-01 13:24:06 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 全部在职人员工资表
2025-09-01 13:24:06.093 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-01 13:24:06.203 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-01 13:24:06.206 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-09-01 13:24:06 - src.gui.unified_data_import_window - INFO - 预览数据加载完成: 全部在职人员工资表 - 100 行
2025-09-01 13:24:06 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 100 行, 23 列
2025-09-01 13:24:06 - src.gui.unified_data_import_window - INFO - 已加载Sheet '全部在职人员工资表' 的预览数据: 100 行
2025-09-01 13:24:10.241 | INFO     | src.gui.prototype.prototype_main_window:_show_unified_import_dialog:6005 | 用户取消了数据导入
